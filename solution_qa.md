# 团详页优化需求解决方案设计

## 📋 文档说明

本文档基于`requirement.md`中的需求、`QA.md`中的需求澄清，以及`index.md`对仓库的理解，提出针对团详页优化需求的具体技术方案设计。

## 1. 🎯 方案概述

### 1.1 核心目标
- 优化眼镜、眼科、口腔行业的团详页展示效果
- 支持多选字段、质保信息单位、取镜时间类型等新功能
- 增强用户体验，提供更精准的商品信息展示

### 1.2 技术架构
基于现有DDD架构，采用策略模式处理不同行业的业务逻辑：
```
ProductDetailPageCommonModuleSpiImpl → 模块编排框架 → Strategy处理 → VO构建 → 前端展示
```

## 2. 🔧 技术方案设计

### 2.1 行业通用部分

#### 2.1.1 开店宝后台翻单引导标签
**实现范围：** B端后台功能，不在当前RPC服务范围内

**技术方案：**
- 标签识别逻辑：基于团单类目和必填属性状态判断
- "待升级"标签：当团单类目有必填属性未填写时展示
- "待更新"标签：当团单依然为旧团单类目时展示
- 实现位置：开店宝后台系统，需要单独开发

**数据依赖：**
- 团单类目信息
- 属性填写状态
- 类目映射关系表

### 2.2 双眼部分（眼镜+眼科行业）

#### 2.2.1 质保信息新增单位属性

**技术实现：**

**涉及类：**
- `GlassesKeyInfoStrategy` - 眼镜行业策略
- `EyesKeyInfoStrategy` - 眼科行业策略
- `DealDetailStructuredDetailVO` - 数据传输对象

**实现方案：**
```java
// 在Strategy类中新增质保信息构建方法
private Optional<DealDetailStructuredDetailVO> buildWarrantyInfo(ProductAttr productAttr) {
    String lensAssure = productAttr.getSkuAttrFirstValue("lens_assure");
    String lensAssureUnit = productAttr.getSkuAttrFirstValue("lens_assure_unit"); // 新增单位字段
    String frameWarranty = productAttr.getSkuAttrFirstValue("frame_warranty");
    String frameWarrantyUnit = productAttr.getSkuAttrFirstValue("frame_warranty_unit"); // 新增单位字段
    
    StringBuilder content = new StringBuilder();
    if (StringUtils.isNotBlank(lensAssure)) {
        content.append("镜片").append(lensAssure).append(lensAssureUnit != null ? lensAssureUnit : "天").append("质保");
    }
    if (StringUtils.isNotBlank(frameWarranty)) {
        if (content.length() > 0) content.append("，");
        content.append("镜框").append(frameWarranty).append(frameWarrantyUnit != null ? frameWarrantyUnit : "天").append("质保");
    }
    
    return DealDetailStructuredUtils.buildContentAndPopupData("质保信息", content.toString(), "");
}
```

**数据字段：**
- `lens_assure` - 镜片质保期限
- `lens_assure_unit` - 镜片质保单位（年/天）
- `frame_warranty` - 镜框质保期限  
- `frame_warranty_unit` - 镜框质保单位（年/天）

#### 2.2.2 取镜时间类型新增

**技术实现：**

**涉及类：**
- `GlassesKeyInfoStrategy`
- `EyesKeyInfoStrategy`

**实现方案：**
```java
private Optional<DealDetailStructuredDetailVO> buildAcquireTime(ProductAttr productAttr) {
    String acquireTimeType = productAttr.getSkuAttrFirstValue("acquire_time_type");
    String acquireTimeValue = productAttr.getSkuAttrFirstValue("acquire_time_value");
    String acquireTimeRangeStart = productAttr.getSkuAttrFirstValue("acquire_time_range_start");
    String acquireTimeRangeEnd = productAttr.getSkuAttrFirstValue("acquire_time_range_end");
    
    String content = "";
    switch (acquireTimeType) {
        case "immediate":
            content = "立等可取";
            break;
        case "after_days":
            content = acquireTimeValue + "天后可取";
            break;
        case "within_days":
            content = acquireTimeValue + "天内可取";
            break;
        case "range_days":
            content = acquireTimeRangeStart + " - " + acquireTimeRangeEnd + "天可取";
            break;
    }
    
    return DealDetailStructuredUtils.buildTitleAndContent("取镜时间", content);
}
```

**数据字段：**
- `acquire_time_type` - 取镜时间类型（immediate/after_days/within_days/range_days）
- `acquire_time_value` - 取镜时间数值
- `acquire_time_range_start` - 取镜时间范围开始
- `acquire_time_range_end` - 取镜时间范围结束

#### 2.2.3 验光操作人员多选支持

**技术实现：**

**现有实现基础：**
- `EyesAttrUtils.handleMultiValueAttr()` - 已实现多值属性处理
- `EyesAttrUtils.buildOptometristBubblePopupData()` - 已实现气泡提示

**优化方案：**
```java
// 在GlassesKeyInfoStrategy中
private Optional<DealDetailStructuredDetailVO> buildOptometryWithoutDot(ProductAttr productAttr, Long shopCount) {
    List<String> optometrist = productAttr.getSkuAttrValue("optometrist2");
    if (CollectionUtils.isEmpty(optometrist)) {
        return Optional.empty();
    }
    
    String experienceStr = productAttr.getSkuAttrFirstValue("optometrist_experience");
    if (optometrist.size() == 1) {
        String content = StringUtils.isNotBlank(experienceStr) ? 
            String.format("%s（从业%s）", optometrist.get(0), experienceStr) : optometrist.get(0);
        return DealDetailStructuredUtils.buildTitleAndContent("验光操作", content);
    }
    
    String bubblePopupData = "";
    if (shopCount != null && shopCount > 1) {
        bubblePopupData = JSON.toJSONString(DealDetailStructuredDetailVO.builder()
                .content("该团购多门店可用，实际验光操作人员可联系商家确认")
                .build());
    }
    
    return DealDetailStructuredUtils.buildContentAndPopupData("验光操作", 
        String.join("/", optometrist) + "可选", bubblePopupData);
}
```

#### 2.2.4 度数/折射率说明入口及浮层

**技术实现：**

**涉及类：**
- `GlassesKeyInfoStrategy`

**实现方案：**
```java
private Optional<DealDetailStructuredDetailVO> buildDegreeRefractionInfo(ProductAttr productAttr) {
    // 检查是否为配镜类目
    if (!isOpticalCategory(productAttr)) {
        return Optional.empty();
    }
    
    String popupData = JSON.toJSONString(DealDetailStructuredDetailVO.builder()
            .type(ViewComponentTypeEnum.POPUP_LAYER.getType())
            .title("度数/折射率关系说明")
            .content(buildDegreeRefractionContent())
            .build());
    
    return DealDetailStructuredUtils.buildContentAndPopupData("", "度数/折射率说明", popupData);
}

private String buildDegreeRefractionContent() {
    return "度数与折射率的关系说明：\n" +
           "1. 低度数（0-300度）：建议选择1.56折射率\n" +
           "2. 中度数（300-600度）：建议选择1.61折射率\n" +
           "3. 高度数（600度以上）：建议选择1.67或1.74折射率";
}
```

#### 2.2.5 镜片技术字段多选支持

**技术实现：**

**涉及类：**
- `GlassesLensAttrUtils`

**实现方案：**
```java
public static Optional<DealDetailStructuredDetailVO> buildLensTechnology(ProductAttr productAttr) {
    List<String> technologies = productAttr.getSkuAttrValues("lens_technology");
    if (CollectionUtils.isEmpty(technologies)) {
        return Optional.empty();
    }
    
    String content = String.join("、", technologies);
    String reportUrl = "提报入口URL"; // 配置化
    String popupData = JSON.toJSONString(DealDetailStructuredDetailVO.builder()
            .content("如添加时找不到对应的镜片技术，可点击提报")
            .jumpUrl(reportUrl)
            .build());
    
    return DealDetailStructuredUtils.buildContentAndPopupData("镜片技术", content, popupData);
}
```

### 2.3 口腔部分

#### 2.3.1 补牙材料科普信息补充

**技术实现：**

**现有实现基础：**
- `DentalKeyInfoStrategy` - 已实现基础逻辑
- `LionConfigUtils.getMaterialComparisonPicMap()` - 已实现图片配置获取

**优化方案：**
```java
// 在DentalKeyInfoStrategy中
private Optional<DealDetailStructuredDetailVO> buildMaterialScienceInfo(ProductAttr productAttr) {
    String technique = productAttr.getSkuAttrFirstValue("Technique");
    String materialBrand = productAttr.getSkuAttrFirstValue("MaterialBrand");
    
    if (!Objects.equals(technique, "树脂补牙") || !Objects.equals(materialBrand, "3M")) {
        return Optional.empty();
    }
    
    String materialModel = productAttr.getSkuAttrFirstValue("MaterialModel");
    if (StringUtils.isBlank(materialModel)) {
        return Optional.empty();
    }
    
    materialModel = materialModel.toUpperCase();
    Map<String, String> materialComparisonPicMap = LionConfigUtils.getMaterialComparisonPicMap();
    String materialModelIcon = materialComparisonPicMap.get(materialModel);
    
    return Optional.of(DealDetailStructuredDetailVO.builder()
            .type(ViewComponentTypeEnum.SCIENCE_INFO.getType())
            .title("3M树脂材料系列对比")
            .icon(materialModelIcon)
            .content(buildMaterialDescription(materialModel))
            .build());
}

private String buildMaterialDescription(String materialModel) {
    switch (materialModel) {
        case "Z250":
            return "适用于后牙修复，强度高，耐磨性好";
        case "Z350":
            return "适用于前后牙修复，美观性佳，抛光性好";
        case "P60":
            return "适用于后牙修复，操作简便，性价比高";
        case "P90":
            return "适用于前牙修复，美观度极佳，颜色匹配度高";
        default:
            return "3M优质树脂材料，安全可靠";
    }
}
```

#### 2.3.2 服务时长范围逻辑

**技术实现：**

**涉及类：**
- `DentalKeyInfoStrategy`

**实现方案：**
```java
private Optional<DealDetailStructuredDetailVO> buildServiceDuration(ProductAttr productAttr) {
    String durationStart = productAttr.getSkuAttrFirstValue("service_duration_start");
    String durationEnd = productAttr.getSkuAttrFirstValue("service_duration_end");
    String duration = productAttr.getSkuAttrFirstValue("service_duration");
    
    String content = "";
    if (StringUtils.isNotBlank(durationStart) && StringUtils.isNotBlank(durationEnd)) {
        content = durationStart + " - " + durationEnd + "分钟";
    } else if (StringUtils.isNotBlank(duration)) {
        content = duration + "分钟";
    }
    
    if (StringUtils.isBlank(content)) {
        return Optional.empty();
    }
    
    return DealDetailStructuredUtils.buildTitleAndContent("服务时长", content);
}
```

#### 2.3.3 套餐包含新增属性

**技术实现：**

**涉及类：**
- `DentalKeyInfoStrategy`

**实现方案：**
```java
private Optional<DealDetailStructuredDetailVO> buildPackageIncludes(ProductAttr productAttr) {
    List<String> packageIncludes = productAttr.getSkuAttrValues("package_includes");
    if (CollectionUtils.isEmpty(packageIncludes)) {
        return Optional.empty();
    }
    
    String content = String.join("、", packageIncludes);
    return DealDetailStructuredUtils.buildTitleAndContent("套餐包含", content);
}
```

## 3. 📊 数据结构设计

### 3.1 新增属性字段

**眼镜/眼科行业：**
```java
// 质保信息单位
lens_assure_unit: String // 镜片质保单位（年/天）
frame_warranty_unit: String // 镜框质保单位（年/天）

// 取镜时间类型
acquire_time_type: String // 取镜时间类型
acquire_time_value: String // 取镜时间数值
acquire_time_range_start: String // 取镜时间范围开始
acquire_time_range_end: String // 取镜时间范围结束

// 镜片技术多选
lens_technology: List<String> // 镜片技术列表
```

**口腔行业：**
```java
// 服务时长范围
service_duration_start: String // 服务时长开始
service_duration_end: String // 服务时长结束

// 套餐包含
package_includes: List<String> // 套餐包含项目列表
```

### 3.2 VO字段扩展

**DealDetailStructuredDetailVO已有字段：**
- `titleFontWeight` - 标题字体加粗（已实现）
- `popupData` - 弹窗浮层数据（已实现）
- `icon` - 图标地址（已实现）

## 4. 🔄 实施计划

### 4.1 开发阶段

**阶段一：基础功能实现（1-2周）**
1. 验光操作人员多选优化
2. 质保信息单位支持
3. 口腔材料科普信息完善

**阶段二：高级功能实现（2-3周）**
1. 取镜时间类型新增
2. 镜片技术字段多选
3. 服务时长范围逻辑

**阶段三：用户体验优化（1周）**
1. 度数/折射率说明入口
2. 套餐包含属性
3. 气泡提示优化

### 4.2 测试验证

**单元测试：**
- Strategy类方法测试
- 工具类方法测试
- VO构建逻辑测试

**集成测试：**
- RPC服务调用测试
- 数据获取链路测试
- 前端展示效果测试

### 4.3 上线部署

**灰度发布：**
1. 小流量验证（5%）
2. 中流量验证（20%）
3. 全量发布（100%）

**监控指标：**
- 接口响应时间
- 错误率监控
- 用户体验指标

## 5. 🎯 预期效果

### 5.1 业务价值
- 提升团详页信息展示的准确性和完整性
- 增强用户对商品信息的理解和信任度
- 支持商家更好地展示产品特色和优势

### 5.2 技术价值
- 完善多行业业务逻辑处理能力
- 提升代码复用性和可维护性
- 增强系统扩展性和灵活性

## 6. 🚨 风险控制

### 6.1 技术风险
- **数据兼容性：** 新增字段需要兼容现有数据
- **性能影响：** 多值属性处理可能影响响应时间
- **缓存失效：** 属性变更可能导致缓存数据不一致

### 6.2 缓解措施
- 实施渐进式数据迁移
- 优化数据获取和处理逻辑
- 完善缓存更新机制
- 建立完整的回滚方案

---

**文档版本：** v1.0  
**创建时间：** 2025-01-27  
**负责团队：** 产品详情页通用模块团队
