# 团详页优化需求分析（基于当前仓库代码逻辑）

## 📋 文档说明

本文档基于原始需求和当前仓库代码逻辑，重点澄清**业务概念边界**、**技术实现现状**和**需求复杂度评估**，旨在指导更准确的技术方案设计。

## 1. 🆕 业务概念澄清和边界定义

### 1.1 核心概念澄清

**关键概念映射**：
| 需求术语 | 实际业务概念 | 技术实现 | 说明 |
|----------|-------------|----------|------|
| **验光操作人员** | 眼科/眼镜行业的验光操作人员 | optometrist2字段，支持多选 | 从单选改为多选，支持多门店场景 |
| **质保信息** | 眼镜产品的质保期限 | lens_assure, frame_warranty等字段 | 新增单位属性支持"年"与"天" |
| **取镜时间** | 眼镜配镜完成时间 | acquire_time相关字段 | 支持多种时间类型表达 |
| **材料型号科普** | 口腔补牙材料的科普信息 | MaterialModel字段+科普图片 | 仅3M树脂补牙材料显示 |

### 1.2 业务关系澄清

**Q1: 验光操作人员的多选逻辑是如何实现的？**
A1: 根据代码实现，验光操作人员字段从`optometrist`单选改为`optometrist2`多选。当为单选时显示"操作人员（从业经验）"，当为多选时显示"操作人员1/操作人员2可选"，并在多门店场景下提供气泡提示。

**Q2: 质保信息的单位支持是如何处理的？**
A2: 代码中通过`titleFontWeight`字段实现标题加粗，质保信息支持"年"和"天"两种单位，展示格式为"镜片X天质保，镜框Y天质保"或"Z天质保"。

## 2. 现有技术实现现状分析

### 2.1 现有代码基础

**已有实现**：
- `GlassesKeyInfoStrategy`: 处理眼镜行业关键信息展示
- `EyesKeyInfoStrategy`: 处理眼科行业关键信息展示  
- `DentalKeyInfoStrategy`: 处理口腔行业关键信息展示
- `EyesAttrUtils`: 眼科属性处理工具类，新增多值属性处理方法
- `DealDetailStructuredDetailVO`: 团详结构化数据VO，新增`titleFontWeight`字段

### 2.2 数据链路现状

**现有数据流**：
团购属性数据 → ProductAttr → 各Strategy处理 → DealDetailStructuredDetailVO → 前端展示

**已有数据字段**：
- 眼镜行业: `optometrist2`(多选), `lens_assure`, `frame_warranty`, `acquire_time`
- 眼科行业: `OperatorsSelection`(多选), `optometry_data`
- 口腔行业: `MaterialModel`, `MaterialBrand`, `Technique`

**已有数据来源的接口**：
通过`ProductAttr.getSkuAttrValue()`获取多值属性，`getSkuAttrFirstValue()`获取单值属性

### 2.3 需求复杂度准确评估

**Q3: 多选字段的实现复杂度如何？**
A3: 中等复杂度。需要修改数据获取方式从`getSkuAttrFirstValue`改为`getSkuAttrValue`，并实现多值展示逻辑。代码中通过`EyesAttrUtils.handleMultiValueAttr()`统一处理多值属性。

**Q4: 气泡提示功能的实现方式？**
A4: 通过`buildOptometristBubblePopupData()`方法实现，当多选且多门店时显示"该团购多门店可用，实际验光操作人员可联系商家确认"的气泡提示。

## 3. 业务范围

**Q5: 此次优化涉及哪些行业和类目？**
A5: 主要涉及三个行业：
- 眼镜行业：近视配镜、儿童配镜、老花眼镜、仅镜框、仅镜片、太阳眼镜、隐形眼镜
- 眼科行业：儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜
- 口腔行业：补牙、儿童补牙相关类目

## 4. 需求内容

### 4.1 核心需求澄清

**Q6: 开店宝后台的"待升级"和"待更新"标签如何实现？**
A6: 根据需求描述，这部分功能不在当前代码变更范围内，属于B端后台功能，需要单独实现。

**Q7: 标题拼接逻辑的"｜"替换"|"是如何处理的？**
A7: 这是业务侧的调整，从模式3（固定前拼接）调整为模式2（推荐前拼接），具体实现不在当前代码变更中体现。

### 4.2 实际实现的功能点

**功能点1：验光操作人员多选支持**
- 眼镜行业使用`optometrist2`字段
- 眼科行业使用`OperatorsSelection`字段
- 多选时以"/"分隔显示，单选时显示经验信息

**功能点2：质保信息单位支持**
- 支持"年"和"天"两种单位
- 通过`titleFontWeight`实现标题加粗
- 兼容现有数据格式

**功能点3：口腔材料科普信息**
- 仅当`Technique=树脂补牙`且`MaterialBrand=3M`时显示
- 通过`LionConfigUtils.getMaterialComparisonPicMap()`获取图片配置
- 支持Z250/Z350/P60/P90等不同型号

## 5. 需求描述中存在的问题

**Q8: 需求中提到的取镜时间类型新增功能在代码中如何体现？**
A8: 代码中删除了`GlassesLensAttrUtils.buildAcquireTime()`方法，说明取镜时间逻辑可能迁移到其他地方或者实现方式发生变化，需要进一步确认具体实现位置。

**Q9: 镜片技术字段多选功能的实现状态？**
A9: 需求中提到镜片技术字段支持多选，但在当前代码变更中未找到相关实现，可能在其他模块或后续迭代中实现。

**Q10: 服务时长范围逻辑的具体实现？**
A10: 需求提到口腔服务时长支持范围值展示（如"10-20分钟"），但在当前代码变更中未找到具体实现，需要确认实现状态。

**其他需补充或澄清的问题：**
- 度数/折射率说明入口的具体实现位置
- 套餐包含新增属性的数据结构定义
- 健康检查模块商家说明的默认文案处理逻辑
